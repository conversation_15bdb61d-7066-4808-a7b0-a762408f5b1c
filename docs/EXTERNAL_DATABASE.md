# Configuración de Base de Datos Externa

Este documento describe cómo configurar y usar la conexión a la base de datos externa PostgreSQL para obtener información de productos.

## Configuración

### Variables de Entorno

Agrega las siguientes variables de entorno a tu archivo `.env`:

```env
# External Database Configuration
EXTERNAL_POSTGRES_DB_URL=*****************************************
EXTERNAL_POSTGRES_USER=user
EXTERNAL_POSTGRES_PASSWORD=1234
```

### Estructura de la Tabla Externa

La aplicación espera que la base de datos externa tenga una tabla llamada `product` con la siguiente estructura mínima:

```sql
CREATE TABLE product (
    product_id INTEGER PRIMARY KEY,
    name VARCHAR(255) NOT NULL
);
```

## API Endpoints

### Obtener todos los productos externos

```http
GET /api/external-products
```

**Respuesta:**
```json
[
    {
        "productId": 1,
        "name": "Producto Ejemplo 1"
    },
    {
        "productId": 2,
        "name": "Producto Ejemplo 2"
    }
]
```

### Obtener un producto específico

```http
GET /api/external-products/{id}
```

**Respuesta:**
```json
{
    "productId": 1,
    "name": "Producto Ejemplo 1"
}
```

### Sincronizar productos

```http
POST /api/external-products/sync
```

**Respuesta:**
```json
{
    "message": "Sincronización completada",
    "count": 5
}
```

## Arquitectura

La implementación sigue el patrón de arquitectura hexagonal:

- **Dominio**: `ExternalProduct` - Modelo de dominio para productos externos
- **Puerto de Entrada**: `ExternalProductService` - Interfaz del servicio
- **Puerto de Salida**: `ExternalProductRepositoryPort` - Interfaz del repositorio
- **Adaptador**: `JdbcExternalProductRepository` - Implementación usando JDBC
- **Controlador**: `ExternalProductController` - API REST

## Configuración de Conexión

La conexión a la base de datos externa se configura en `ExternalDatabase.kt` con las siguientes características:

- Pool de conexiones: 5 conexiones máximo
- AutoCommit: true (para consultas de solo lectura)
- Timeout de conexión: 30 segundos
- Timeout de idle: 10 minutos
- Tiempo de vida máximo: 30 minutos

## Manejo de Errores

La aplicación maneja los siguientes tipos de errores:

- **SQLException**: Errores de conexión o consulta SQL
- **Validation**: Validación de parámetros de entrada
- **NotFound**: Producto no encontrado

Todos los errores se devuelven como respuestas JSON con el código de estado HTTP apropiado.

## Testing

Se incluyen tests unitarios que usan un repositorio mock para verificar la funcionalidad sin depender de la base de datos externa.

Para ejecutar los tests:

```bash
./gradlew test
```
