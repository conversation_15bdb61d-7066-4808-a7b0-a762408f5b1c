package com.gnico.majo.adapter.persistence

import com.gnico.majo.application.domain.model.ExternalProduct
import com.gnico.majo.application.port.out.ExternalProductRepositoryPort

/**
 * Implementación mock del repositorio de productos externos para tests
 */
class MockExternalProductRepository : ExternalProductRepositoryPort {
    
    private val mockProducts = listOf(
        ExternalProduct.create(1, "Producto Externo 1"),
        ExternalProduct.create(2, "Producto Externo 2"),
        ExternalProduct.create(3, "Producto Externo 3")
    )

    override suspend fun findAllProducts(): List<ExternalProduct> {
        return mockProducts
    }

    override suspend fun findProductById(productId: Int): ExternalProduct? {
        return mockProducts.find { it.productId == productId }
    }
}
