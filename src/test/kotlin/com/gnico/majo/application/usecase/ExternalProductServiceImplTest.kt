package com.gnico.majo.application.usecase

import com.gnico.majo.adapter.persistence.MockExternalProductRepository
import com.gnico.majo.application.domain.model.ExternalProduct
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull

class ExternalProductServiceImplTest {

    private lateinit var externalProductService: ExternalProductServiceImpl
    private lateinit var mockRepository: MockExternalProductRepository

    @BeforeEach
    fun setUp() {
        mockRepository = MockExternalProductRepository()
        externalProductService = ExternalProductServiceImpl(mockRepository)
    }

    @Test
    fun `getAllExternalProducts should return all products`() = runBlocking {
        // When
        val products = externalProductService.getAllExternalProducts()

        // Then
        assertEquals(3, products.size)
        assertEquals("Producto Externo 1", products[0].name)
        assertEquals(1, products[0].productId)
    }

    @Test
    fun `getExternalProductById should return product when exists`() = runBlocking {
        // When
        val product = externalProductService.getExternalProductById(1)

        // Then
        assertNotNull(product)
        assertEquals(1, product.productId)
        assertEquals("Producto Externo 1", product.name)
    }

    @Test
    fun `getExternalProductById should return null when product does not exist`() = runBlocking {
        // When
        val product = externalProductService.getExternalProductById(999)

        // Then
        assertNull(product)
    }

    @Test
    fun `syncExternalProducts should return count of available products`() = runBlocking {
        // When
        val count = externalProductService.syncExternalProducts()

        // Then
        assertEquals(3, count)
    }
}
