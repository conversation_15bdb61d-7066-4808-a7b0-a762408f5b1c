-- Limpiar datos existentes de tipos_iva
DELETE FROM tipos_iva;

-- Reiniciar la secuencia del ID para tipos_iva
ALTER SEQUENCE tipos_iva_id_seq RESTART WITH 1;

-- Insertar nuevos datos para tipos_iva con IDs específicos
INSERT INTO tipos_iva (id, porcentaje, descripcion) VALUES
(3, 0.00, 'Exento'),
(4, 10.50, '10.5%'),
(5, 21.00, '21%'),
(6, 27.00, '27%'),
(8, 5.00, '5%'),
(9, 2.50, '2.5%');

-- Actualizar la secuencia para que el próximo ID sea mayor que el máximo insertado
SELECT setval('tipos_iva_id_seq', (SELECT MAX(id) FROM tipos_iva));

-- Limpiar la tabla productos para que empiece vacía
DELETE FROM productos;