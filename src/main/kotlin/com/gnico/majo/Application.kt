package com.gnico.majo

import com.gnico.majo.adapter.controller.rest.ProductoController
import com.gnico.majo.adapter.controller.rest.SaleController
import com.gnico.majo.adapter.controller.rest.ExternalProductController
import com.gnico.majo.infrastructure.routes.configureProductoRoutes
import com.gnico.majo.infrastructure.routes.configureSaleRoutes
import com.gnico.majo.infrastructure.routes.configureExternalProductRoutes
import com.gnico.majo.infrastructure.config.Database
import com.gnico.majo.infrastructure.config.ExternalDatabase
import com.gnico.majo.infrastructure.config.appModule
import io.ktor.serialization.kotlinx.json.json
import io.ktor.server.application.*
import io.ktor.server.plugins.contentnegotiation.ContentNegotiation
import org.koin.core.context.stopKoin
import org.koin.ktor.plugin.Koin
import org.koin.ktor.ext.inject
import org.koin.logger.slf4jLogger


fun main(args: Array<String>) {
    io.ktor.server.netty.EngineMain.main(args)
}

fun Application.module() {
    Database.init()
    ExternalDatabase.init()

    install(ContentNegotiation) {
        json()
    }

    install(Koin) {
        slf4jLogger()
        modules(appModule)
    }

    val saleController by inject<SaleController>()
    configureSaleRoutes(saleController)

    val productoController by inject<ProductoController>()
    configureProductoRoutes(productoController)

    val externalProductController by inject<ExternalProductController>()
    configureExternalProductRoutes(externalProductController)

    monitor.subscribe(ApplicationStopping) {
        stopKoin()
        Database.close()
        ExternalDatabase.close()
    }
}

