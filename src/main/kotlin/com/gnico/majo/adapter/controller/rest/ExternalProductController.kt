package com.gnico.majo.adapter.controller.rest

import com.gnico.majo.application.port.`in`.ExternalProductService
import io.ktor.http.*
import io.ktor.server.response.*
import io.ktor.server.routing.*

class ExternalProductController(
    private val externalProductService: ExternalProductService
) {

    suspend fun getAllExternalProducts(call: RoutingCall) {
        try {
            val products = externalProductService.getAllExternalProducts()
            call.respond(HttpStatusCode.OK, products)
        } catch (e: Exception) {
            call.respond(
                HttpStatusCode.InternalServerError,
                mapOf("error" to "Error al obtener productos externos: ${e.message}")
            )
        }
    }

    suspend fun getExternalProductById(call: RoutingCall) {
        try {
            val productId = call.parameters["id"]?.toIntOrNull()
            if (productId == null) {
                call.respond(
                    HttpStatusCode.BadRequest,
                    mapOf("error" to "ID de producto inválido")
                )
                return
            }

            val product = externalProductService.getExternalProductById(productId)
            if (product != null) {
                call.respond(HttpStatusCode.OK, product)
            } else {
                call.respond(
                    HttpStatusCode.NotFound,
                    mapOf("error" to "Producto externo no encontrado")
                )
            }
        } catch (e: Exception) {
            call.respond(
                HttpStatusCode.InternalServerError,
                mapOf("error" to "Error al obtener producto externo: ${e.message}")
            )
        }
    }

    suspend fun syncExternalProducts(call: RoutingCall) {
        try {
            val syncedCount = externalProductService.syncExternalProducts()
            call.respond(
                HttpStatusCode.OK,
                mapOf("message" to "Sincronización completada", "count" to syncedCount)
            )
        } catch (e: Exception) {
            call.respond(
                HttpStatusCode.InternalServerError,
                mapOf("error" to "Error al sincronizar productos: ${e.message}")
            )
        }
    }
}
