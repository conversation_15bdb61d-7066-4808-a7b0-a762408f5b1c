package com.gnico.majo.adapter.persistence

import com.gnico.majo.application.domain.model.ExternalProduct
import com.gnico.majo.application.port.out.ExternalProductRepositoryPort
import com.gnico.majo.infrastructure.config.ExternalDatabase
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.sql.SQLException

class JdbcExternalProductRepository : ExternalProductRepositoryPort {

    override suspend fun findAllProducts(): List<ExternalProduct> = withContext(Dispatchers.IO) {
        val products = mutableListOf<ExternalProduct>()
        
        try {
            ExternalDatabase.getConnection().use { connection ->
                val sql = "SELECT product_id, name FROM product"
                connection.prepareStatement(sql).use { statement ->
                    statement.executeQuery().use { resultSet ->
                        while (resultSet.next()) {
                            val productId = resultSet.getInt("product_id")
                            val name = resultSet.getString("name")
                            
                            if (!resultSet.wasNull()) {
                                products.add(ExternalProduct.create(productId, name))
                            }
                        }
                    }
                }
            }
        } catch (e: SQLException) {
            throw RuntimeException("Error al obtener productos de la base de datos externa", e)
        }
        
        return@withContext products
    }

    override suspend fun findProductById(productId: Int): ExternalProduct? = withContext(Dispatchers.IO) {
        try {
            ExternalDatabase.getConnection().use { connection ->
                val sql = "SELECT product_id, name FROM product WHERE product_id = ?"
                connection.prepareStatement(sql).use { statement ->
                    statement.setInt(1, productId)
                    statement.executeQuery().use { resultSet ->
                        if (resultSet.next()) {
                            val id = resultSet.getInt("product_id")
                            val name = resultSet.getString("name")
                            
                            if (!resultSet.wasNull()) {
                                return@withContext ExternalProduct.create(id, name)
                            }
                        }
                    }
                }
            }
        } catch (e: SQLException) {
            throw RuntimeException("Error al obtener producto $productId de la base de datos externa", e)
        }
        
        return@withContext null
    }
}
