package com.gnico.majo.infrastructure.config

import com.gnico.majo.utils.Env
import com.zaxxer.hikari.HikariConfig
import com.zaxxer.hikari.HikariDataSource
import java.sql.Connection

object ExternalDatabase {

    private lateinit var dataSource: HikariDataSource

    fun init() {
        val config = HikariConfig().apply {
            jdbcUrl = Env.get("EXTERNAL_POSTGRES_DB_URL")
            username = Env.get("EXTERNAL_POSTGRES_USER")
            password = Env.get("EXTERNAL_POSTGRES_PASSWORD")
            maximumPoolSize = 5
            isAutoCommit = true // Para consultas simples de solo lectura
            connectionTimeout = 30000
            idleTimeout = 600000
            maxLifetime = 1800000
        }
        dataSource = HikariDataSource(config)
    }

    fun getConnection(): Connection {
        return dataSource.connection
    }

    fun close() {
        if (::dataSource.isInitialized) {
            dataSource.close()
        }
    }
}
