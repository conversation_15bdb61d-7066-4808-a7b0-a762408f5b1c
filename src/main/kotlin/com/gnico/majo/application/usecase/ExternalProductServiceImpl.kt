package com.gnico.majo.application.usecase

import com.gnico.majo.application.domain.model.ExternalProduct
import com.gnico.majo.application.port.`in`.ExternalProductService
import com.gnico.majo.application.port.out.ExternalProductRepositoryPort

class ExternalProductServiceImpl(
    private val externalProductRepository: ExternalProductRepositoryPort
) : ExternalProductService {

    override suspend fun getAllExternalProducts(): List<ExternalProduct> {
        return externalProductRepository.findAllProducts()
    }

    override suspend fun getExternalProductById(productId: Int): ExternalProduct? {
        require(productId >= 0) { "Product ID debe ser mayor a 0" }
        return externalProductRepository.findProductById(productId)
    }

    override suspend fun syncExternalProducts(): Int {
        // Esta implementación básica solo cuenta los productos disponibles
        // En una implementación más completa, aquí se podría:
        // 1. Obtener productos externos
        // 2. Compararlos con productos locales
        // 3. Insertar/actualizar productos en la DB local
        // 4. Retornar el número de productos sincronizados
        
        val externalProducts = externalProductRepository.findAllProducts()
        return externalProducts.size
    }
}
