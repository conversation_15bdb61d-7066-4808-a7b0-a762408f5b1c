package com.gnico.majo.application.port.`in`

import com.gnico.majo.application.domain.model.ExternalProduct

/**
 * Puerto de entrada para el servicio de productos externos
 */
interface ExternalProductService {
    
    /**
     * Obtiene todos los productos de la base de datos externa
     * @return Lista de productos externos
     */
    suspend fun getAllExternalProducts(): List<ExternalProduct>
    
    /**
     * Obtiene un producto específico por su ID de la base de datos externa
     * @param productId ID del producto a buscar
     * @return Producto externo si existe, null en caso contrario
     */
    suspend fun getExternalProductById(productId: Int): ExternalProduct?
    
    /**
     * Sincroniza productos externos con la base de datos local
     * Esto podría ser útil para importar productos de la DB externa a la local
     * @return Número de productos sincronizados
     */
    suspend fun syncExternalProducts(): Int
}
