package com.gnico.majo.application.domain.model

import kotlinx.serialization.Serializable

/**
 * Representa un producto obtenido de la base de datos externa
 */
@Serializable
data class ExternalProduct(
    val productId: Int,
    val name: String
) {
    companion object {
        fun create(productId: Int, name: String): ExternalProduct {
            require(productId >= 0) { "Product ID debe ser mayor a 0" }
            require(name.isNotBlank()) { "Name no puede estar vacío" }

            return ExternalProduct(
                productId = productId,
                name = name.trim()
            )
        }
    }
}
